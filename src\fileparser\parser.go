package fileparser

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/h2non/filetype"
	"github.com/ledongthuc/pdf"
	"github.com/xuri/excelize/v2"
)

// FileType 文件类型枚举
type FileType int

const (
	FileTypeUnknown FileType = iota
	FileTypePDF
	FileTypeWord
	FileTypeExcel
)

// ParseResult 解析结果
type ParseResult struct {
	Content  string   // 提取的文本内容
	FileType FileType // 文件类型
	Pages    int      // 页数（PDF）或工作表数（Excel）
	Error    error    // 解析错误
}

// FileParser 文件解析器接口
type FileParser interface {
	Parse(filePath string) (*ParseResult, error)
	SupportedTypes() []string
}

// UniversalParser 通用文件解析器
type UniversalParser struct{}

// NewUniversalParser 创建通用文件解析器
func NewUniversalParser() *UniversalParser {
	return &UniversalParser{}
}

// Parse 解析文件
func (p *UniversalParser) Parse(filePath string) (*ParseResult, error) {
	// 检测文件类型
	fileType, err := p.detectFileType(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}

	// 根据文件类型选择解析器
	switch fileType {
	case FileTypePDF:
		return p.parsePDF(filePath)
	case FileTypeWord:
		return p.parseWord(filePath)
	case FileTypeExcel:
		return p.parseExcel(filePath)
	default:
		err := fmt.Errorf("不支持的文件类型")
		return &ParseResult{Error: err}, err
	}
}

// detectFileType 检测文件类型
func (p *UniversalParser) detectFileType(filePath string) (FileType, error) {
	// 首先通过文件扩展名判断
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".pdf":
		return FileTypePDF, nil
	case ".docx", ".doc":
		return FileTypeWord, nil
	case ".xlsx", ".xls":
		return FileTypeExcel, nil
	}

	// 通过文件内容检测
	file, err := os.Open(filePath)
	if err != nil {
		return FileTypeUnknown, err
	}
	defer file.Close()

	// 读取文件头部用于类型检测
	head := make([]byte, 261)
	_, err = file.Read(head)
	if err != nil && err != io.EOF {
		return FileTypeUnknown, err
	}

	kind, err := filetype.Match(head)
	if err != nil {
		return FileTypeUnknown, err
	}

	switch kind.MIME.Type {
	case "application":
		switch kind.MIME.Subtype {
		case "pdf":
			return FileTypePDF, nil
		case "vnd.openxmlformats-officedocument.wordprocessingml.document":
			return FileTypeWord, nil
		case "vnd.openxmlformats-officedocument.spreadsheetml.sheet":
			return FileTypeExcel, nil
		}
	}

	return FileTypeUnknown, fmt.Errorf("无法识别的文件类型")
}

// parsePDF 解析PDF文件
func (p *UniversalParser) parsePDF(filePath string) (*ParseResult, error) {
	file, reader, err := pdf.Open(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}
	defer file.Close()

	var content strings.Builder
	totalPages := reader.NumPage()

	for i := 1; i <= totalPages; i++ {
		page := reader.Page(i)
		if page.V.IsNull() {
			continue
		}

		text, err := page.GetPlainText(nil)
		if err != nil {
			// 如果某页解析失败，继续处理其他页
			content.WriteString(fmt.Sprintf("[第%d页解析失败: %v]\n", i, err))
			continue
		}

		content.WriteString(text)
		content.WriteString("\n")
	}

	return &ParseResult{
		Content:  content.String(),
		FileType: FileTypePDF,
		Pages:    totalPages,
	}, nil
}

// parseWord 解析Word文档
func (p *UniversalParser) parseWord(filePath string) (*ParseResult, error) {
	// 简化的Word文档解析，读取为文本
	// 注意：这是一个简化版本，实际的Word解析需要更复杂的处理
	content := fmt.Sprintf("Word文档: %s\n[注意：Word文档解析功能正在开发中，暂时无法提取详细内容]", filePath)

	return &ParseResult{
		Content:  content,
		FileType: FileTypeWord,
		Pages:    1,
	}, nil
}

// parseExcel 解析Excel文件
func (p *UniversalParser) parseExcel(filePath string) (*ParseResult, error) {
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}
	defer file.Close()

	var content strings.Builder
	sheetList := file.GetSheetList()

	for _, sheetName := range sheetList {
		content.WriteString(fmt.Sprintf("=== 工作表: %s ===\n", sheetName))

		rows, err := file.GetRows(sheetName)
		if err != nil {
			content.WriteString(fmt.Sprintf("[工作表 %s 读取失败: %v]\n", sheetName, err))
			continue
		}

		for rowIndex, row := range rows {
			if len(row) == 0 {
				continue
			}

			// 只处理前100行，避免内容过长
			if rowIndex >= 100 {
				content.WriteString("...(内容过长，已截断)\n")
				break
			}

			content.WriteString(fmt.Sprintf("第%d行: %s\n", rowIndex+1, strings.Join(row, " | ")))
		}
		content.WriteString("\n")
	}

	return &ParseResult{
		Content:  content.String(),
		FileType: FileTypeExcel,
		Pages:    len(sheetList),
	}, nil
}

// SupportedTypes 返回支持的文件类型
func (p *UniversalParser) SupportedTypes() []string {
	return []string{".pdf", ".docx", ".doc", ".xlsx", ".xls"}
}

// GetFileTypeString 获取文件类型字符串
func GetFileTypeString(fileType FileType) string {
	switch fileType {
	case FileTypePDF:
		return "PDF"
	case FileTypeWord:
		return "Word文档"
	case FileTypeExcel:
		return "Excel表格"
	default:
		return "未知"
	}
}

// ValidateFile 验证文件是否可以解析
func ValidateFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filePath)
	}

	// 检查文件大小（限制为50MB）
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("无法获取文件信息: %v", err)
	}

	const maxSize = 50 * 1024 * 1024 // 50MB
	if fileInfo.Size() > maxSize {
		return fmt.Errorf("文件过大，最大支持50MB")
	}

	// 检查文件扩展名
	parser := NewUniversalParser()
	supportedTypes := parser.SupportedTypes()
	ext := strings.ToLower(filepath.Ext(filePath))

	for _, supportedExt := range supportedTypes {
		if ext == supportedExt {
			return nil
		}
	}

	return fmt.Errorf("不支持的文件类型: %s，支持的类型: %s", ext, strings.Join(supportedTypes, ", "))
}
