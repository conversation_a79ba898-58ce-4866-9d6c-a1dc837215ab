package ocr

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"path/filepath"
	"strings"

	"github.com/ledongthuc/pdf"
	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"simple_inventory_management_system/internal/logger"
)

// PDFEnhancer PDF增强处理器
type PDFEnhancer struct {
	ocrService *OCRService
}

// PDFEnhancedResult PDF增强处理结果
type PDFEnhancedResult struct {
	TextContent   string            `json:"text_content"`   // 文本内容
	ImageTexts    []ImageTextResult `json:"image_texts"`    // 图片OCR结果
	TableResults  []TableResult     `json:"table_results"`  // 表格识别结果
	Pages         int               `json:"pages"`          // 页数
	HasImages     bool              `json:"has_images"`     // 是否包含图片
	HasTables     bool              `json:"has_tables"`     // 是否包含表格
	Error         error             `json:"error"`          // 错误信息
}

// ImageTextResult 图片文本识别结果
type ImageTextResult struct {
	PageNumber int     `json:"page_number"` // 页码
	ImageIndex int     `json:"image_index"` // 图片索引
	Text       string  `json:"text"`        // 识别的文本
	Confidence float64 `json:"confidence"`  // 置信度
}

// NewPDFEnhancer 创建PDF增强处理器
func NewPDFEnhancer() *PDFEnhancer {
	return &PDFEnhancer{
		ocrService: NewOCRService(),
	}
}

// EnhancePDF 增强处理PDF文件
func (p *PDFEnhancer) EnhancePDF(pdfPath string) (*PDFEnhancedResult, error) {
	result := &PDFEnhancedResult{
		ImageTexts:   make([]ImageTextResult, 0),
		TableResults: make([]TableResult, 0),
	}

	// 检查文件是否存在
	if _, err := os.Stat(pdfPath); os.IsNotExist(err) {
		result.Error = fmt.Errorf("PDF文件不存在: %s", pdfPath)
		return result, result.Error
	}

	// 提取文本内容
	textContent, pageCount, err := p.extractTextFromPDF(pdfPath)
	if err != nil {
		logger.Log.WithField("error", err).Error("提取PDF文本失败")
		result.Error = err
		return result, err
	}

	result.TextContent = textContent
	result.Pages = pageCount

	// 提取图片并进行OCR
	imageTexts, err := p.extractAndOCRImages(pdfPath)
	if err != nil {
		logger.Log.WithField("error", err).Warn("PDF图片OCR处理失败")
	} else {
		result.ImageTexts = imageTexts
		result.HasImages = len(imageTexts) > 0
	}

	// 检测和识别表格
	tableResults, err := p.detectAndRecognizeTables(pdfPath)
	if err != nil {
		logger.Log.WithField("error", err).Warn("PDF表格识别失败")
	} else {
		result.TableResults = tableResults
		result.HasTables = len(tableResults) > 0
	}

	logger.Log.WithField("pages", result.Pages).
		WithField("images", len(result.ImageTexts)).
		WithField("tables", len(result.TableResults)).
		Info("PDF增强处理完成")

	return result, nil
}

// extractTextFromPDF 从PDF提取文本内容
func (p *PDFEnhancer) extractTextFromPDF(pdfPath string) (string, int, error) {
	file, reader, err := pdf.Open(pdfPath)
	if err != nil {
		return "", 0, fmt.Errorf("打开PDF文件失败: %v", err)
	}
	defer file.Close()

	var content strings.Builder
	pageCount := reader.NumPage()

	for i := 1; i <= pageCount; i++ {
		page := reader.Page(i)
		if page.V.IsNull() {
			continue
		}

		content.WriteString(fmt.Sprintf("=== 第%d页 ===\n", i))
		
		// 提取页面文本
		pageText, err := page.GetPlainText()
		if err != nil {
			logger.Log.WithField("page", i).WithField("error", err).Warn("提取页面文本失败")
			continue
		}

		content.WriteString(pageText)
		content.WriteString("\n\n")
	}

	return content.String(), pageCount, nil
}

// extractAndOCRImages 提取PDF中的图片并进行OCR
func (p *PDFEnhancer) extractAndOCRImages(pdfPath string) ([]ImageTextResult, error) {
	var results []ImageTextResult

	// 创建临时目录存储提取的图片
	tempDir, err := os.MkdirTemp("", "pdf_images_*")
	if err != nil {
		return nil, fmt.Errorf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 使用pdfcpu提取图片
	err = api.ExtractImagesFile(pdfPath, tempDir, nil, nil)
	if err != nil {
		logger.Log.WithField("error", err).Warn("提取PDF图片失败")
		return results, nil // 不返回错误，继续处理其他内容
	}

	// 遍历提取的图片文件
	err = filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 检查是否为图片文件
		if !IsImageFile(path) {
			return nil
		}

		// 对图片进行OCR
		ocrResult, err := p.ocrService.RecognizeImage(path)
		if err != nil {
			logger.Log.WithField("image", path).WithField("error", err).Warn("图片OCR失败")
			return nil
		}

		if strings.TrimSpace(ocrResult.Text) != "" {
			// 从文件名解析页码和图片索引
			pageNum, imgIndex := p.parseImageFileName(info.Name())
			
			results = append(results, ImageTextResult{
				PageNumber: pageNum,
				ImageIndex: imgIndex,
				Text:       ocrResult.Text,
				Confidence: ocrResult.Confidence,
			})
		}

		return nil
	})

	if err != nil {
		logger.Log.WithField("error", err).Warn("遍历图片文件失败")
	}

	return results, nil
}

// detectAndRecognizeTables 检测和识别PDF中的表格
func (p *PDFEnhancer) detectAndRecognizeTables(pdfPath string) ([]TableResult, error) {
	var results []TableResult

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "pdf_tables_*")
	if err != nil {
		return nil, fmt.Errorf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 将PDF页面转换为图片
	pageImages, err := p.convertPDFToImages(pdfPath, tempDir)
	if err != nil {
		logger.Log.WithField("error", err).Warn("PDF转图片失败")
		return results, nil
	}

	// 对每个页面图片进行表格识别
	for pageNum, imagePath := range pageImages {
		tableResult, err := p.ocrService.RecognizeTable(imagePath)
		if err != nil {
			logger.Log.WithField("page", pageNum).WithField("error", err).Warn("页面表格识别失败")
			continue
		}

		if len(tableResult.Cells) > 0 {
			results = append(results, *tableResult)
		}
	}

	return results, nil
}

// convertPDFToImages 将PDF页面转换为图片
func (p *PDFEnhancer) convertPDFToImages(pdfPath, outputDir string) (map[int]string, error) {
	pageImages := make(map[int]string)

	// 使用pdfcpu将PDF转换为图片
	conf := model.NewDefaultConfiguration()
	conf.ValidationMode = model.ValidationRelaxed

	// 这里简化处理，实际应该使用更专业的PDF到图片转换库
	// 由于pdfcpu的图片转换API比较复杂，这里先返回空结果
	logger.Log.Info("PDF转图片功能需要进一步实现")
	
	return pageImages, nil
}

// parseImageFileName 从图片文件名解析页码和图片索引
func (p *PDFEnhancer) parseImageFileName(fileName string) (int, int) {
	// 简单的文件名解析，实际应该根据pdfcpu的命名规则来解析
	// 默认返回页码1，图片索引0
	return 1, 0
}

// GetEnhancedContent 获取增强后的完整内容
func (result *PDFEnhancedResult) GetEnhancedContent() string {
	var content strings.Builder

	// 添加原始文本内容
	if result.TextContent != "" {
		content.WriteString("=== PDF文本内容 ===\n")
		content.WriteString(result.TextContent)
		content.WriteString("\n\n")
	}

	// 添加图片OCR内容
	if len(result.ImageTexts) > 0 {
		content.WriteString("=== 图片识别内容 ===\n")
		for _, imgText := range result.ImageTexts {
			content.WriteString(fmt.Sprintf("第%d页图片%d (置信度: %.2f):\n%s\n\n",
				imgText.PageNumber, imgText.ImageIndex, imgText.Confidence, imgText.Text))
		}
	}

	// 添加表格内容
	if len(result.TableResults) > 0 {
		content.WriteString("=== 表格识别内容 ===\n")
		for i, table := range result.TableResults {
			content.WriteString(fmt.Sprintf("表格%d (%d行 x %d列):\n", i+1, table.Rows, table.Cols))
			
			// 按行列组织表格数据
			tableData := make(map[int]map[int]string)
			for _, cell := range table.Cells {
				if tableData[cell.Row] == nil {
					tableData[cell.Row] = make(map[int]string)
				}
				tableData[cell.Row][cell.Column] = cell.Text
			}

			// 输出表格
			for row := 0; row < table.Rows; row++ {
				var rowData []string
				for col := 0; col < table.Cols; col++ {
					if cellText, exists := tableData[row][col]; exists {
						rowData = append(rowData, cellText)
					} else {
						rowData = append(rowData, "")
					}
				}
				content.WriteString(strings.Join(rowData, " | "))
				content.WriteString("\n")
			}
			content.WriteString("\n")
		}
	}

	return content.String()
}
